/* Critical CSS for above-the-fold content - inlined for performance */

/* Hero section critical styles */
.home-hero-section {
  background: #000e1f;
  border-radius: 0px 0px 80px 80px;
}

.home-hero-bg {
  position: relative;
  overflow: hidden;
}

.home-hero-container {
  padding: 169px 72px 220px 72px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: center;
}

.hero-content h4 {
  color: #fff;
  text-align: center;
  font-size: 46px;
  font-weight: 700;
  line-height: 120%;
  margin: 0;
}

.hero-content span {
  color: #0070f5;
}

.hero-content p {
  color: #f9f9f9;
  text-align: center;
  font-size: 26px;
  font-weight: 500;
  line-height: 120%;
  margin: 0;
}

/* Job search form critical styles */
.job-search-top {
  background: #fff;
  border-radius: 16px;
  padding: 16px;
}

.blue-background {
  background: #fff;
  border-radius: 12px;
  padding: 8px;
}

.form-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-input-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  flex: 1;
}

.field-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  min-height: 47px;
}

.job-search-cta {
  background-color: #fdca40 !important;
  color: #151515 !important;
  border-radius: 32px !important;
  height: 47px !important;
  display: flex;
  width: 140px;
  padding: 14px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  font-family: Archivo;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
  border: none !important;
}

/* Mobile optimizations */
@media screen and (max-width: 768px) {
  .home-hero-section {
    padding-bottom: 110px;
  }
  
  .home-hero-container {
    padding: 40px 12px 216px 12px;
    gap: 40px;
  }
  
  .hero-content h4 {
    font-size: 32px;
    font-weight: 700;
    line-height: 110%;
  }
  
  .hero-content p {
    font-size: 20px;
    font-weight: 500;
    line-height: 120%;
  }
  
  .job-search-top {
    padding: 8px;
    border-radius: 10px;
  }
  
  .form-input-container {
    grid-template-columns: repeat(1, 1fr) !important;
  }
  
  .form-container {
    flex-direction: column;
  }
  
  .field-container {
    width: 100%;
    grid-column: span 1 / span 1 !important;
  }
  
  .job-search-cta {
    width: 100% !important;
  }
  
  .form-submit-container {
    width: 100%;
  }
}

/* Loading placeholders */
.loading-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Font display optimization */
@font-face {
  font-family: 'Archivo';
  font-display: swap;
}

/* Prevent layout shift */
.hero-content h4,
.hero-content p {
  contain: layout;
}

/* Optimize rendering */
.home-hero-bg {
  will-change: transform;
  transform: translateZ(0);
}
