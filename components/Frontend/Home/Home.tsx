import React, {useState, useContext, useEffect} from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {useRouter} from 'next/router';
import home_1 from '../../../public/images/home/<USER>';
import AuthContext from '@/Context/AuthContext';
import <PERSON><PERSON>r<PERSON>and<PERSON> from '@/lib/ErrorHandler';
import {notification, Button, Select, Input, Form} from 'antd';
import {Settings} from '@/lib/types';
import dynamic from 'next/dynamic';
import JsonLd from '@/components/JsonLd';
import styles from './Home.module.css';
import {useForm} from 'antd/lib/form/Form';
import {ConnectCompanies} from '@/components/Home/ConnectCompanies';

// Dynamic imports for better performance
const TopJobsOpenings = dynamic(() => import('@/components/Home/TopJobOpening'), {
  loading: () => <div style={{height: '400px', background: '#f5f5f5'}} />,
});
const FindJobsByLocation = dynamic(() => import('./FindJobsByLocation'), {
  loading: () => <div style={{height: '300px', background: '#f5f5f5'}} />,
});
const TopBlogs = dynamic(() => import('@/components/Common/TopBlogs'), {
  loading: () => <div style={{height: '400px', background: '#f5f5f5'}} />,
});
const HighestOffers = dynamic(
  () => import('@/components/Home/HighestOffers').then(mod => ({default: mod.HighestOffers})),
  {
    loading: () => <div style={{height: '300px', background: '#f5f5f5'}} />,
  },
);
const KnowYourWorthCard = dynamic(
  () => import('@/components/Home/KnowYourWorth').then(mod => ({default: mod.KnowYourWorthCard})),
  {
    loading: () => <div style={{height: '200px', background: '#f5f5f5'}} />,
  },
);
const NeedHelpCard = dynamic(() => import('@/components/Home/NeedHelp').then(mod => ({default: mod.NeedHelpCard})), {
  loading: () => <div style={{height: '100px', background: '#f5f5f5'}} />,
});
const LookingToHireCard = dynamic(
  () => import('@/components/Home/LookingTohire').then(mod => ({default: mod.LookingToHireCard})),
  {
    loading: () => <div style={{height: '100px', background: '#f5f5f5'}} />,
  },
);
const Testimonial = dynamic(() => import('@/components/Home/Testimonial').then(mod => ({default: mod.Testimonial})), {
  loading: () => <div style={{height: '400px', background: '#f5f5f5'}} />,
});
const JobFilterGroup = dynamic(
  () => import('@/components/Jobs/JobFilterGroup').then(mod => ({default: mod.JobFilterGroup})),
  {
    loading: () => <div style={{height: '200px', background: '#f5f5f5'}} />,
  },
);
import {useHandleHire} from '@/hooks/useHandleHireUser';
import useWindowDimensions from '@/helpers/useWindowDimensions';

interface HomeProps {
  settings: Settings;
  countries: any;
  testimonials: any;
  latestJob: any;
  sectors: any;
}

export default function Home({settings, countries, testimonials, latestJob, sectors}: HomeProps) {
  const router = useRouter();
  const {user} = useContext(AuthContext);
  const [form] = useForm();
  const {handleClickShowErrorMessage, uploadCVLink} = useHandleHire();
  const {width} = useWindowDimensions();
  const isMobile = (width || 1024) <= 768;

  const handleApplyJob = (role: string) => {
    if (!user) {
      sessionStorage.setItem('signup.role', role);
      router.push('/auth/signup/step-1').then();
    } else router.push('/jobs-in-gulf');
  };

  const submitSearchForm = (values: any) => {
    const filteredOptions = Object.keys(values)
      .filter(function (k) {
        return values[k] != null;
      })
      .reduce(function (acc: any, k) {
        acc[k] = values[k];
        return acc;
      }, {});
    router
      .push(
        {
          pathname: '/jobs-in-gulf',
          query: filteredOptions,
        },
        undefined,
        {shallow: false},
      )
      .then();
    return;
  };

  return (
    <>
      {/* <Head>
        <title>The Talent Point - Empowerment and Connection in Job Search and Recruitment</title>
        <meta
          name="description"
          content="Join The Talent Point - where empowerment and connection drive job search and recruitment. Simplify your job search or hiring process with our platform dedicated to bridging the gap between job seekers and employers."
        />
        <meta name="keywords" content="The Talent Point, job search, recruitment, job seekers, employers" /> */}

      {/* Open Graph tags */}
      {/* <meta
          property="og:title"
          content="The Talent Point - Empowerment and Connection in Job Search and Recruitment"
        />
        <meta
          property="og:description"
          content="Join The Talent Point - where empowerment and connection drive job search and recruitment. Simplify your job search or hiring process with our platform dedicated to bridging the gap between job seekers and employers."
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={process.env.NEXT_PUBLIC_BASE_URL} />
        <meta property="og:type" content="website" /> */}

      {/* Twitter card */}
      {/* <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@TheTalentPoint" />
        <meta
          name="twitter:title"
          content="The Talent Point - Empowerment and Connection in Job Search and Recruitment"
        />
        <meta
          name="twitter:description"
          content="Join The Talent Point - where empowerment and connection drive job search and recruitment. Simplify your job search or hiring process with our platform dedicated to bridging the gap between job seekers and employers."
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head> */}

      <section className={styles['home-hero-section']}>
        <div className={styles['home-hero-bg']}>
          {/* Optimized hero background image */}
          <Image
            src={isMobile ? '/images/home/<USER>' : '/images/home/<USER>'}
            alt="Young professional team"
            fill
            priority
            quality={85}
            sizes="100vw"
            style={{
              objectFit: 'cover',
              objectPosition: 'center',
              zIndex: -1,
            }}
          />
          <div className={`${styles['home-hero-container']} `}>
            <div className={styles['hero-content']}>
              <h4>
                Where <span>talent</span> meets <span>opportunity</span>
              </h4>
              <p>Find jobs you’ll love in Dubai.</p>
            </div>
            <div className={`${styles['job-search-top']} container`}>
              <div className={styles['blue-background']}>
                <Form
                  form={form}
                  onFinish={submitSearchForm}
                  size={'large'}
                  initialValues={router.query}
                  className={styles['form-container']}>
                  <div className={`${styles['form-input-container']}`}>
                    <div
                      className={styles['field-container']}
                      style={{
                        gridColumn: 'span 2 / span 2',
                      }}>
                      <Image src="/icons/search.svg" alt="search" width={21} height={21} />
                      <Form.Item name={'keywords'} noStyle>
                        <Input
                          allowClear
                          bordered={false}
                          style={{width: '100%'}}
                          placeholder={'Job Title, Keyword, Company or Phrase'}
                        />
                      </Form.Item>
                    </div>
                    <div className={styles['field-container']}>
                      <Image src="/icons/location.svg" alt="location" width={21} height={26} />
                      <Form.Item name={'country'} noStyle>
                        <Select
                          allowClear
                          bordered={false}
                          style={{width: '100%'}}
                          showSearch
                          optionFilterProp={'label'}
                          placeholder={'Select country'}
                          options={countries?.map((country: any) => {
                            return {value: `${country.country_name.toString()}`, label: country.country_name};
                          })}
                        />
                      </Form.Item>
                    </div>
                  </div>
                  <div className={styles['form-submit-container']}>
                    <Button htmlType={'submit'} type={'primary'} block className={styles['job-search-cta']}>
                      Find Jobs
                    </Button>
                  </div>
                </Form>
              </div>
            </div>
          </div>
          <ConnectCompanies />
        </div>
      </section>

      <TopJobsOpenings latestJob={latestJob} />
      <section className={`${styles['banner-container']} `} id="job-platform">
        <div className={` bannerChange container`}>
          <h3 className={`"bannerText" ${styles['banner-title']}`} aria-level={3} role="heading">
            We are the #1 Platform for <span className="text-warning">Emirati recruitment</span>
          </h3>
          <h3 className={styles['banner-text']}>
            Discover the Leading Job Platform for Emirati Talent in the Middle East!
          </h3>
          <div className="mt-3">
            <Link
              href={uploadCVLink}
              onClick={
                user?.role == 'employee'
                  ? e => handleClickShowErrorMessage(e, 'You need to register as a employer to hire candidates')
                  : undefined
              }
              prefetch={false}>
              <Button className="hoverChange" style={{height: 'auto'}}>
                Hire Qualified Emiratis
              </Button>
            </Link>
            <Button
              onClick={() => handleApplyJob('employer')}
              className="hoverChange hoverChange1  mx-md-2"
              style={{height: 'auto'}}>
              Browse Now
            </Button>
          </div>
          <div className={styles['emiratis-image-container']}>
            <Image
              src="/images/home/<USER>"
              alt="Emirati professionals"
              width={430}
              height={273}
              quality={80}
              loading="lazy"
              sizes="(max-width: 768px) 0px, 430px"
            />
          </div>
        </div>
      </section>
      <HighestOffers />
      <FindJobsByLocation />

      <section className={`${styles['card-section']} container`}>
        <div>
          <KnowYourWorthCard />
        </div>
        <div className={styles.card_section_right}>
          <NeedHelpCard />
          <LookingToHireCard />
        </div>
      </section>

      <section className={styles.testimonial_section}>
        <Testimonial testimonials={testimonials} />
      </section>

      <TopBlogs />

      <JobFilterGroup countries={countries} sectors={sectors} />

      <JsonLd
        data={{
          '@context': 'https://schema.org',
          '@type': 'Organization',
          image: home_1,
          url: settings?.website_url,
          sameAs: [process.env.NEXT_PUBLIC_BASE_URL],
          logo: process.env.NEXT_PUBLIC_BASE_URL + 'images/Avatars-4.png',
          name: 'The Talent Point',
          description: settings?.homepage_meta_description || '',
          email: '<EMAIL>',
          telephone: '+97143316688',
          vatID: '',
          iso6523Code: '',
        }}
      />
    </>
  );
}
