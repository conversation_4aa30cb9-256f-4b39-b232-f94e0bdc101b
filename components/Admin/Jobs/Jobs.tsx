import React, {useState, useEffect, useContext} from 'react';
import swal from 'sweetalert';
import * as XLSX from 'xlsx';
import Image from 'next/image';
import {
  getAllJobsSearch,
  getAllSectors,
  getAllSkills,
  getAllCountries,
  getAlljobfilter,
  UpdateSaveJobFilterData,
  DeleteJobFilter,
} from '@/lib/frontendapi';
import {deleteJob} from '@/lib/adminapi';
import ModalForm from '@/components/Common/ModalForm';

import {Button, Pagination, notification, Dropdown, Menu} from 'antd';
import {PlusOutlined} from '@ant-design/icons';
import CreateJobForm from '@/components/Jobs/CreateJobForm';
import ContentTitle from '@/components/Common/ContentTitle';
import {Job, Country, PaginationMeta} from '@/lib/types';
import AuthContext from '@/Context/AuthContext';
import {useRouter} from 'next/router';
import {searchJobs} from '@/lib/ApiAdapter';
import axios from 'axios';
import ErrorHandler from '@/lib/ErrorHandler';
import {DownOutlined} from '@ant-design/icons';

interface Sector {
  id?: number;
  sector_name?: string;
}

interface Skill {
  id?: number;
  skills?: string;
}
import type {PaginationProps} from 'antd';
import LoadingIndicator from '@/components/Common/LoadingIndicator';
import {getCurrentUserData} from '@/lib/session';
import {useExportJobs} from '@/modules/admin/mutation/useExportJobs';
import moment from 'moment';

const experienceRanges = [
  {id: 0, label: 'Choose Experience'},
  {id: 1, label: 'Fresher'},
  {id: 2, label: '0-1'},
  {id: 3, label: '2-3'},
  {id: 4, label: '3-5'},
  {id: 5, label: '5-7'},
  {id: 6, label: '7-10'},
  {id: 7, label: '10-15'},
  {id: 8, label: '15-20'},
  {id: 9, label: '20+'},
];

export default function Jobs() {
  const router = useRouter();
  const [openNewJobModal, setOpenNewJobModal] = useState(false);
  const [modalConfirm4, setModalConfirm4] = useState(false);
  const [modalConfirm5, setModalConfirm5] = useState(false);

  const [sectors, setSectors] = useState<Sector[]>([]);
  const [skill, setSkill] = useState<Skill[]>([]);

  const [selectedJobType, setSelectedJobType] = useState<any[]>([]);

  const [selectedSectors, setSelectedSectors] = useState<any[]>([]);
  const [selectedSkill, setSelectedSkill] = useState<any[]>([]);

  const [keywords, setKeywords] = useState('');
  const [location, setLocation] = useState('');

  const [countries, setCountries] = useState<Country[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);

  const [experienceRange, setExperienceRange] = useState(0);
  const [experience, setExperience] = useState(experienceRanges[0].label);

  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [maxSalary, setMaxSalary] = useState<number>(100000);
  const [selectedSalary, setSelectedSalary] = useState<number>(0);

  const [filterJobData, setFilterJobs] = useState([]);
  const [filterjobId, setFilterJobsId] = useState('1');
  const [filterjobsectionname, setFilterJobsSectionName] = useState('');

  const [filterjobtype, setFilterJobsType] = useState('');

  const [filterjobIndex, setFilterJobIndex] = useState(0);

  const [searchSectorKeywords, setSearchSectorKeywords] = useState('');
  const [searchskillKeywords, setSearchskillKeywords] = useState('');
  const {user} = useContext(AuthContext);
  const [reload, setReload] = useState(false);
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta>();
  const [pageSize, setPageSize] = useState(10);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [editJobData, setEditJobData]: any = useState([]);
  const {mutate: exportJobs} = useExportJobs();

  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    console.log('pagination1', current);
    console.log('pagination2', pageSize);
    setPage(current);
    setPageSize(pageSize);
  };

  useEffect(() => {
    setLoading(true);
    const cancelTokenSource = axios.CancelToken.source();
    searchJobs({page: page, pageSize: pageSize}, cancelTokenSource)
      .then(res => {
        if (res?.data) {
          setPaginationMeta(res.meta);
          setJobs(res?.data);
          setLoading(false);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    return cancelTokenSource.cancel;
  }, [page, pageSize]);

  useEffect(() => {
    if (!user) {
      return;
    }

    getAllCountries()
      .then(res => {
        if (res) {
          setCountries(res);
        } else {
          setCountries([]);
        }
      })
      .catch(err => {
        console.log(err);
      });

    getAlljobfilterData(user.id).then();
    getAllSectorsData().then();
    getAllSkillsData().then();
  }, [user, reload]);

  const modalConfirmOpen4 = () => {
    setModalConfirm4(true);
  };
  const modalConfirmClose4 = () => {
    setModalConfirm4(false);
  };
  const modalConfirmOpen5 = () => {
    setModalConfirm5(true);
  };
  const modalConfirmClose5 = () => {
    setModalConfirm5(false);
  };

  const getAllSectorsData = async () => {
    try {
      const res = await getAllSectors();
      if (res.success == true) {
        setSectors(res.sectors);
      } else {
        setSectors([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const getAllSkillsData = async () => {
    try {
      const res = await getAllSkills();
      if (res.success == true) {
        setSkill(res.data);
      } else {
        setSkill([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const getAlljobfilterData = async (id: any) => {
    try {
      const res = await getAlljobfilter(id);
      if (res.status == true) {
        setFilterJobs(res.data);
        if (!filterjobtype && res.data.length > 0) {
          setFilterJobsId(res.data[0].id);
          setFilterJobsSectionName(res.data[0].section_name);
          handleJobFilterNav(res.data[0]);
        }
      } else {
        setFilterJobs([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleChangeSearchBySector = (e: any, sectorId: any) => {
    if (e.target.checked) {
      setSelectedSectors((prevSectors: any[]) => [...prevSectors, sectorId]);
    } else {
      const updatedSectors = selectedSectors.filter(id => id !== sectorId);
      setSelectedSectors(updatedSectors);
    }
  };

  const handleChangeJobType = (e: any) => {
    const jobType = e.target.value;

    setSelectedJobType((prevJobTypes: any) => {
      if (e.target.checked) {
        return [...prevJobTypes, jobType];
      } else {
        return prevJobTypes.filter((type: any) => type !== jobType);
      }
    });
  };

  const handleChangeSearchskill = (e: any, skillId: number) => {
    if (e.target.checked) {
      setSelectedSkill((prevSkills: any[]) => [...prevSkills, skillId]);
    } else {
      const updatedSkills = selectedSkill.filter(id => id !== skillId);
      setSelectedSkill(updatedSkills);
    }
  };

  const handleSubmit = () => {
    setReload(!reload);
    const current_user_data: any = getCurrentUserData();

    const data = {
      keywords: keywords,
      location: location,
      skill: selectedSkill.join(','),
      job_type: selectedJobType.join(','),
      sector: selectedSectors.join(','),
      experience: experienceRange != 0 ? experience : null,
      currency: selectedSalary > 0 ? selectedCurrency : null,
      minsalary: selectedSalary > 0 ? selectedSalary : null,
      maxSalary: maxSalary,
      user_id: current_user_data.id,
      filter_id: filterjobId,
      section_name: filterjobsectionname,
    };

    getAllJobsSearch(data)
      .then(res => {
        if (res.status === 200) {
          // setTotalJobs(res.data);
          // console.log(res, 'res');

          // const paginatedPosts = paginate(res.data, 1, pageSize);

          // console.log(paginatedPosts, 'paginatedPosts');
          setJobs(res?.data?.data);
          handleJobfilterdata(data);
          modalConfirmClose4();
          setPaginationMeta(res.data.meta);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleJobfilterdata = (data: any) => {
    UpdateSaveJobFilterData(data)
      .then(res => {
        console.log(res, 'res');
        if (res.status == true) {
          getAlljobfilterData(user?.id);
          setFilterJobsId(res.id);
          setModalConfirm5(false);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const formatDate = (dateString: any) => {
    const postedDate = new Date(dateString);
    const currentDate = new Date();
    const timeDiff = currentDate.getTime() - postedDate.getTime();
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

    if (daysDiff === 0) {
      return 'Posted today';
    } else if (daysDiff === 1) {
      return 'Posted yesterday';
    } else {
      return `Posted ${daysDiff} days ago`;
    }
  };

  const handleExperienceChange = (event: any) => {
    const selectedRange = Number(event.target.value);
    setExperienceRange(selectedRange);

    // Find the corresponding experience label based on the selected range
    const selectedExperience = experienceRanges.find(range => range.id === selectedRange);
    if (selectedExperience) {
      setExperience(selectedExperience.label);
    }
  };

  const handleCurrencyChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    if (event.target.value != '') {
      setSelectedCurrency(event.target.value);
    } else {
      setSelectedSalary(0);
      setSelectedCurrency('');
    }
  };

  const handleSalaryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (selectedCurrency) {
      setSelectedSalary(Number(event.target.value));
    } else {
      notification.info({message: 'Please choose a currency first'});
    }
  };

  const handleJobFilterEdit = (id: any, name: any, index: any) => {
    setFilterJobsId(id);
    setFilterJobsSectionName(name ?? '');
    setFilterJobsType('edit');
    setFilterJobIndex(index);
    setModalConfirm5(true);
  };

  const handleSubmitjobfilter = (e: any) => {
    e.preventDefault();
    const data = {
      filter_id: filterjobtype == 'edit' ? filterjobId : '',
      section_name: filterjobsectionname,
      user_id: user?.id,
      job_title: '',
      country_id: '',
      currency: '',
      salary: '',
      experience: '',
      skills: '',
      sector: '',
      job_type: '',
    };

    UpdateSaveJobFilterData(data)
      .then(res => {
        if (res.status == true) {
          getAlljobfilterData(user?.id);
          setFilterJobsId(res.id);
          setModalConfirm5(false);
          handleJobFilterNav(res.data);
        } else {
          setJobs([]);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleJobFilterNav = (job: any) => {
    //console.log(job);

    setFilterJobsId(job.id);
    setFilterJobsSectionName(job.section_name);

    if (job.country_id) {
      setLocation(job.country_id);
    } else {
      setLocation('');
    }

    if (job.job_title) {
      setKeywords(job.job_title);
    } else {
      setKeywords('');
    }

    if (job.currency) {
      setSelectedCurrency(job.currency);
      setSelectedSalary(job.salary > 0 ? job.salary : 0);
    } else {
      setSelectedCurrency('');
      setSelectedSalary(0);
    }

    if (job.experience) {
      if (job.experience == 'fresher') {
        setExperienceRange(1);
      }
      if (job.experience == '0-1') {
        setExperienceRange(2);
      }
      if (job.experience == '2-3') {
        setExperienceRange(3);
      }
      if (job.experience == '3-5') {
        setExperienceRange(4);
      }
      if (job.experience == '5-7') {
        setExperienceRange(4);
      }
      if (job.experience == '7-10') {
        setExperienceRange(6);
      }
      if (job.experience == '10-15') {
        setExperienceRange(7);
      }
      if (job.experience == '15-20') {
        setExperienceRange(8);
      }

      if (job.experience == '20+') {
        setExperienceRange(9);
      }

      setExperience(job.experience);
    } else {
      setExperience('Choose Experience');
      setExperienceRange(0);
    }

    if (job.skills) {
      setSelectedSkill(job.skills.split(','));
    } else {
      setSelectedSkill([]);
    }

    if (job.sector) {
      setSelectedSectors(job.sector.split(','));
    } else {
      setSelectedSectors([]);
    }

    if (job.job_type) {
      setSelectedJobType(job.job_type.split(','));
    } else {
      setSelectedJobType([]);
    }

    const data = {
      keywords: job.job_title ? job.job_title : null,
      location: job.country_id ? job.country_id : null,
      skill: job.skills ? job.skills : null,
      job_type: job.job_type ? job.job_type : null,
      sector: job.sector ? job.sector : null,
      currency: job.currency ? job.currency : null,
      minsalary: job.salary ? job.salary : null,
      experience: job.experience ? job.experience : null,
      user_id: user?.id,
      filter_id: job.id,
    };

    searchJobs()
      .then(res => {
        setPaginationMeta(res.meta);
        setJobs(res.data);
        modalConfirmClose4();
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleDeleteJobFilter = (e: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the Job filter Section',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {id: filterjobId};
        DeleteJobFilter(data)
          .then(res => {
            if (res.status === true) {
              notification.success({message: res.message});
              getAlljobfilterDataDelete(user?.id);

              setModalConfirm5(false);
            } else {
              console.log('Deletion failed');
            }
          })
          .catch(err => {
            // Handle error
            console.log(err);
          });
      } else {
        console.log('Deletion cancelled');
      }
    });
  };

  const getAlljobfilterDataDelete = async (id: any) => {
    try {
      const res = await getAlljobfilter(id);
      if (res.status == true) {
        setFilterJobs(res.data);
        setFilterJobsId(res.data[0].id);
        setFilterJobsSectionName(res.data[0].section_name);
        handleJobFilterNav(res.data[0]);
      } else {
        setFilterJobs([]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleClick = (type: any, id: any) => {
    let updatedSkill = [];
    let updatedSector = [];
    let updatedJobType = [];

    if (type == 'skill') {
      updatedSkill = selectedSkill.filter(skillId => skillId != id);
      setSelectedSkill(updatedSkill);
    }

    if (type == 'sector') {
      updatedSector = selectedSectors.filter(SectorId => SectorId != id);
      setSelectedSectors(updatedSector);
    }

    if (type == 'jobtype') {
      updatedJobType = selectedJobType.filter(jobtype => jobtype != id);
      setSelectedJobType(updatedJobType);
    }

    if (type == 'job_title') {
      setKeywords('');
    }

    if (type == 'salary') {
      setSelectedCurrency('');
      setSelectedSalary(0);
    }

    if (type == 'experience') {
      setExperienceRange(0);
    }

    if (type == 'country_id') {
      setLocation('');
    }

    const data = {
      keywords: type == 'job_title' ? '' : keywords,
      location: type == 'country_id' ? '' : location,
      skill: type == 'skill' ? updatedSkill.join(',') : selectedSkill.join(','),
      job_type: type == 'jobtype' ? updatedJobType.join(',') : selectedJobType.join(','),
      sector: type == 'sector' ? updatedSector.join(',') : selectedSectors.join(','),
      experience: type == 'experience' ? '' : experienceRange != 0 ? experience : '',
      currency: type == 'salary' ? '' : selectedCurrency,
      minsalary: type == 'salary' ? '' : selectedSalary > 0 ? selectedSalary : '',
      maxSalary: maxSalary,
      user_id: user?.id,
      filter_id: filterjobId,
      section_name: filterjobsectionname,
    };

    getAllJobsSearch(data)
      .then(res => {
        setPaginationMeta(res.data.meta);
        //const paginatedPosts = paginate(res.data, currentPage, pageSize);
        setJobs(res.data.data);
        handleJobfilterdata(data);
        modalConfirmClose4();
      })
      .catch(err => {
        console.log(err);
      });
  };

  const onSearchskill = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keywords = e.target.value;
    setSearchskillKeywords(keywords);

    if (keywords) {
      const filteredSkills = skill.filter((skill: any) => skill.skills.toLowerCase().includes(keywords.toLowerCase()));
      setSkill(filteredSkills);
    } else {
      getAllSkillsData();
    }
  };

  const onSearchsector = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keywords = e.target.value;
    setSearchSectorKeywords(keywords);

    if (keywords) {
      const filteredSector = sectors.filter((sectors: any) =>
        sectors.sector_name.toLowerCase().includes(keywords.toLowerCase()),
      );
      setSectors(filteredSector);
    } else {
      getAllSectorsData();
    }
  };

  const handleDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete your Job',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        deleteJob(id)
          .then(res => {
            if (res.status === true) {
              swal('Your Job has been deleted!', {icon: 'success'});
              getAlljobfilterData(user?.id);
              const cancelTokenSource = axios.CancelToken.source();
              searchJobs({page: page, pageSize: pageSize}, cancelTokenSource).then(res => {
                setJobs(res?.data);
                setPaginationMeta(res.meta);
              });
            } else {
              notification.error({message: res.message});
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      }
    });
  };

  const editJob = (job: any) => {
    setEditJobData(job);
    setOpenNewJobModal(true);
  };

  const handleExportJobs = () => {
    exportJobs(
      {
        format: 'xlsx',
      },
      {
        onSuccess: data => {
          const url = window.URL.createObjectURL(new Blob([data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `jobs_data_${moment().format('DD-MM-YYYY')}.xlsx`);
          document.body.appendChild(link);
          link.click();
        },
      },
    );
  };

  const menu = (job: any) => (
    <Menu>
      <Menu.Item>
        <a target="_blank" rel="noopener noreferrer" href={'/job/' + job.job_slug}>
          View public profile
        </a>
      </Menu.Item>
      <Menu.Item>
        <a href="#" onClick={() => editJob(job)}>
          Edit Job
        </a>
      </Menu.Item>
      <Menu.Item danger>
        <a href="#" onClick={e => handleDelete(e, job.id)}>
          Delete Job
        </a>
      </Menu.Item>
    </Menu>
  );

  return (
    <>
      <div className="dash-right">
        <ContentTitle
          title={'Jobs'}
          total={paginationMeta?.total}
          tools={
            <>
              <Button
                type={'primary'}
                ghost
                onClick={handleExportJobs}
                icon={<i className="fa-solid fa-file-arrow-down"></i>}>
                Export as .xls
              </Button>
              <Button
                type={'primary'}
                ghost
                icon={<PlusOutlined />}
                onClick={() => {
                  editJob([]);
                  setOpenNewJobModal(true);
                }}>
                Create new job
              </Button>
            </>
          }
        />
        <ModalForm
          open={openNewJobModal}
          title={editJobData.job_title ? `Edit ${editJobData.job_title}` : 'Post a Job'}
          description={'Enter your basic job details information & get started right away.'}
          onCancel={() => setOpenNewJobModal(false)}>
          <CreateJobForm
            onCompleted={async () => {
              setOpenNewJobModal(false);
              setReload(!reload);
            }}
            job={editJobData}
          />
        </ModalForm>
        <ModalForm open={modalConfirm5} title={'Search'} onCancel={modalConfirmClose5}>
          <form onSubmit={handleSubmitjobfilter} className="common_form_error" id="menu_form">
            <div className="popup-body">
              <p className="f-12 c-2C2C2C">{filterjobtype == 'edit' ? 'Edit Search Name' : 'Saved Search Name'}</p>
              <input
                type="text"
                placeholder="Search Name"
                className="big-input mb-0"
                onChange={e => setFilterJobsSectionName(e.target.value)}
                value={filterjobsectionname || ''}
                required></input>

              <div className="text-right mt-3">
                {filterjobtype === 'edit' && filterjobIndex != 0 && (
                  <button className="cancel" onClick={handleDeleteJobFilter}>
                    Delete
                  </button>
                )}
                <button type="submit" className="save">
                  {filterjobtype == 'edit' ? 'Update' : 'Save'}
                </button>
              </div>
            </div>
          </form>
        </ModalForm>

        <div className="tab-filter mt-4">
          <nav>
            <div className="nav nav-tabs" id="nav-tab" role="tablist">
              {filterJobData.map((job: any, index: any) => (
                <button
                  key={index}
                  className={`nav-link ${filterjobId === job.id ? 'active' : ''} ${index == 0 ? '' : 'mx-1'}`}
                  type="button"
                  style={{color: filterjobId === job.id ? '#0d6efd' : 'gray'}}
                  onClick={() => {
                    handleJobFilterNav(job);
                    setFilterJobsType('edit');
                  }}>
                  {job.section_name ? job.section_name : 'Search Name'}{' '}
                  <i
                    className="fa-solid fa-pencil"
                    onClick={() => handleJobFilterEdit(job.id, job.section_name, index)}></i>
                </button>
              ))}
              <button
                className="nav-link"
                onClick={() => {
                  setModalConfirm5(true);
                  setFilterJobsType('add');
                  setFilterJobsSectionName('');
                }}>
                +
              </button>
            </div>
          </nav>

          <div className="tab-content" id="nav-tabContent">
            <div className="tab-pane fade show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
              <div className="filter">
                <div className="filter-sp">
                  <LoadingIndicator visible={loading} />
                  {filterJobData
                    .filter((job_filter: any) => filterjobId == job_filter.id)
                    .map((job_filter: any, index: any) => (
                      <div key={index} className="ASd">
                        {job_filter.job_title && (
                          <p className="cat d-inline-block">
                            {job_filter.job_title}
                            <i className="fa-solid fa-xmark" onClick={() => handleClick('job_title', '')}></i>
                          </p>
                        )}

                        {job_filter.country_id && (
                          <p className="cat d-inline-block mx-2">
                            {
                              countries.find(
                                (country: any) => country.id == job_filter.country_id && country.status == 'active',
                              )?.country_name
                            }
                            <i className="fa-solid fa-xmark" onClick={() => handleClick('country_id', '')}></i>
                          </p>
                        )}

                        {job_filter.salary && (
                          <p className="cat d-inline-block mx-2">
                            0 - {job_filter.salary} {job_filter.currency}
                            <i className="fa-solid fa-xmark" onClick={() => handleClick('salary', '')}></i>
                          </p>
                        )}

                        {job_filter.experience && (
                          <p className="cat d-inline-block mx-2">
                            {job_filter.experience}{' '}
                            {job_filter.experience !== 'fresher' && (job_filter.experience != '0-1' ? 'years' : 'year')}
                            <i className="fa-solid fa-xmark" onClick={() => handleClick('experience', '')}></i>
                          </p>
                        )}

                        {job_filter.skills && (
                          <>
                            {job_filter.skills.split(',').map((skillId: any, index: any) => {
                              const skillObj = skill.find(s => s.id == Number(skillId));
                              return (
                                <p className="cat d-inline-block mx-2" key={index}>
                                  {skillObj ? <span>{skillObj.skills}</span> : null}
                                  <i className="fa-solid fa-xmark" onClick={() => handleClick('skill', skillId)}></i>
                                </p>
                              );
                            })}
                          </>
                        )}

                        {job_filter.sector && (
                          <>
                            {job_filter.sector.split(',').map((sectorId: any, index: any) => {
                              const sector = sectors.find(s => s.id == Number(sectorId));
                              return (
                                <p className="cat d-inline-block mx-2" key={index}>
                                  {sector ? <span>{sector.sector_name}</span> : null}
                                  <i className="fa-solid fa-xmark" onClick={() => handleClick('sector', sectorId)}></i>
                                </p>
                              );
                            })}
                          </>
                        )}

                        {job_filter.job_type && (
                          <>
                            {job_filter.job_type.split(',').map((jobtype: any, index: any) => {
                              return (
                                <p className="cat d-inline-block mx-2" key={index}>
                                  {jobtype ? <span>{jobtype}</span> : null}
                                  <i className="fa-solid fa-xmark" onClick={() => handleClick('jobtype', jobtype)}></i>
                                </p>
                              );
                            })}
                          </>
                        )}
                      </div>
                    ))}
                </div>

                <div className="filter-bottom" style={{cursor: 'pointer'}}>
                  <p
                    onClick={() => {
                      modalConfirmOpen4();
                    }}>
                    <i className="fa-solid fa-angles-down"></i> Filter
                  </p>
                </div>
              </div>
              <ModalForm open={modalConfirm4} onCancel={modalConfirmClose4} title={'Filter by'}>
                <div className="popup-body filter-pop text-left">
                  <div className="row">
                    <div className="col-sm-6 text-left">
                      <p className="f-22 c-0055BA w-700">Filter By</p>
                    </div>
                    <div className="col-sm-6 text-right"></div>
                  </div>

                  <div className="row">
                    <div className="col-sm-6">
                      <div className="form-part mt-4">
                        <input
                          type="text"
                          placeholder="Job Title"
                          className="medium-input"
                          onChange={(e: any) => setKeywords(e.target.value)}
                          value={keywords || ''}
                        />
                      </div>
                    </div>
                    <div className="col-sm-6">
                      <div className="form-part mt-4">
                        {/* <input
                                  type="text"
                                  placeholder="Location"
                                  className="medium-input"
                                  onChange={(e:any) => setLocation(e.target.value)}
                                /> */}
                        <select
                          className="form-control medium-input"
                          onChange={(e: any) => setLocation(e.target.value)}
                          value={location || ''}>
                          <option value="">Select Location</option>
                          {countries.length > 0 ? (
                            countries.map((CountryData: any, index) => {
                              if (CountryData.status === 'active') {
                                return (
                                  <option value={CountryData.id || ''} key={index}>
                                    {CountryData.country_name}
                                  </option>
                                );
                              }
                            })
                          ) : (
                            <option value="">Select Location</option>
                          )}
                        </select>
                      </div>
                    </div>
                  </div>
                  <div className="row">
                    <div className="col-sm-6">
                      <div className="salary-box h-2 mt-4">
                        <select
                          className="choose-currency mb-4"
                          value={selectedCurrency || ''}
                          onChange={handleCurrencyChange}>
                          <option value="">Choose Currency</option>
                          {countries.map((country: any, index: any) => {
                            if (country.currency !== null) {
                              if (country.status === 'active') {
                                return (
                                  <option key={index} value={country.currency || ''}>
                                    {country.currency}
                                  </option>
                                );
                              }
                            }
                            return null; // If currency is null, don't render the option
                          })}
                        </select>
                        <p className="f-16 w-600 c-2C2C2C">Salary</p>

                        <p className="f-12 c-2C2C2C">{`${selectedCurrency} 0 - ${selectedCurrency} ${maxSalary}`}</p>

                        {selectedSalary > 0 && (
                          <p className="f-12 c-2C2C2C">
                            {selectedSalary} {selectedCurrency}
                          </p>
                        )}

                        <input
                          type="range"
                          className="form-range w-75"
                          id="customRange1"
                          min="0"
                          max={maxSalary.toString()}
                          value={selectedSalary.toString() || ''}
                          onChange={handleSalaryChange}
                        />
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="salary-box h-2 mt-4">
                        <p className="f-16 w-600 c-2C2C2C pb-2">Experience</p>
                        <p className="f-12 c-2C2C2C">
                          {experience}{' '}
                          {experienceRange !== 0 &&
                            experienceRange !== 1 &&
                            (experienceRange == 2 ? 'year' : 'years')}{' '}
                        </p>
                        <input
                          type="range"
                          className="form-range w-75"
                          id="customRange1"
                          min="0"
                          max="9"
                          step="1"
                          value={experienceRange || ''}
                          onChange={handleExperienceChange}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-sm-6">
                      <div className="salary-box mt-4">
                        <p className="f-16 w-600 c-2C2C2C">Sector</p>
                        <div className="form-in position-relative input-bababa ">
                          <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                          <input
                            type="text"
                            placeholder="Search Sectors"
                            className="medium-input left-sp mt-2 mb-2 c-999999"
                            value={searchSectorKeywords || ''}
                            onChange={e => onSearchsector(e)}
                          />
                        </div>
                        <div className="form-in" id="scrolldata">
                          {sectors.length > 0
                            ? sectors.map((sectors_data: any, index: any) => {
                                return (
                                  <div className="form-master-field dflex" key={index}>
                                    <input
                                      type="checkbox"
                                      placeholder="Placeholder"
                                      className="master-fields checkbox border-0 mb-0"
                                      onChange={e => {
                                        handleChangeSearchBySector(e, sectors_data.id.toString());
                                      }}
                                      checked={selectedSectors.includes(sectors_data.id.toString())}
                                    />
                                    <label className="check-label c-4D4D4D mb-0">{sectors_data.sector_name}</label>
                                  </div>
                                );
                              })
                            : null}
                        </div>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="salary-box mt-3">
                        <p className="f-16 w-600 c-2C2C2C pb-2">Skills</p>
                        <div className="form-in position-relative input-bababa ">
                          <i className="fa-solid fa-magnifying-glass  glass-search pad-sp"></i>
                          <input
                            type="text"
                            placeholder="Search Skills"
                            className="medium-input left-sp mt-2 mb-2 c-999999"
                            value={searchskillKeywords || ''}
                            onChange={e => onSearchskill(e)}
                          />
                        </div>
                        <div className="form-in" id="scrolldata">
                          {skill.length > 0
                            ? skill.map((skills: any, index: any) => {
                                return (
                                  <div className="form-master-field dflex" key={index}>
                                    <input
                                      type="checkbox"
                                      placeholder="Placeholder"
                                      className="master-fields checkbox border-0 mb-0"
                                      onChange={e => {
                                        handleChangeSearchskill(e, skills.id.toString());
                                      }}
                                      checked={selectedSkill.includes(skills.id.toString())} // Set the 'checked' attribute based on the 'selectedSkill' array
                                    />
                                    <label className="check-label c-4D4D4D mb-0">{skills.skills}</label>
                                  </div>
                                );
                              })
                            : null}
                        </div>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="salary-box mt-4">
                        <p className="f-16 w-600 c-2C2C2C">Job Type</p>

                        <div className="form-master-field dflex ">
                          <input
                            type="checkbox"
                            placeholder="Placeholder"
                            className="master-fields checkbox border-0 mb-0"
                            value="fulltime"
                            onChange={handleChangeJobType}
                            checked={selectedJobType.includes('fulltime')}
                          />
                          <label className="check-label c-4D4D4D mb-0">Full-Time</label>
                        </div>

                        <div className="form-master-field dflex ">
                          <input
                            type="checkbox"
                            placeholder="Placeholder"
                            className="master-fields checkbox border-0 mb-0"
                            value="parttime"
                            onChange={handleChangeJobType}
                            checked={selectedJobType.includes('parttime')}
                          />
                          <label className="check-label c-4D4D4D mb-0">Part-time</label>
                        </div>

                        <div className="form-master-field dflex ">
                          <input
                            type="checkbox"
                            placeholder="Placeholder"
                            className="master-fields checkbox border-0 mb-0"
                            value="contract"
                            onChange={handleChangeJobType}
                            checked={selectedJobType.includes('contract')}
                          />
                          <label className="check-label c-4D4D4D mb-0">Contract</label>
                        </div>

                        <div className="form-master-field dflex ">
                          <input
                            type="checkbox"
                            placeholder="Placeholder"
                            className="master-fields checkbox border-0 mb-0"
                            value="freelance"
                            onChange={handleChangeJobType}
                            checked={selectedJobType.includes('freelance')}
                          />
                          <label className="check-label c-4D4D4D mb-0">FreeLancer</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-center mt-5">
                    <button
                      className="btn-a primary-size-16 btn-bg-0055BA"
                      onClick={() => {
                        handleSubmit();
                      }}>
                      View Results
                    </button>
                  </div>
                </div>
              </ModalForm>
              {jobs.map((job, index) => (
                <div className="filter filter-sp m-center mt-4 border-radius-16" key={index}>
                  <div className="row">
                    <div className="col pr-0 max-90-col m-none">
                      <Image
                        src={
                          job.company?.logo
                            ? job.company.logo?.thumbnail
                            : process.env.NEXT_PUBLIC_BASE_URL + 'images/logo-img.png'
                        }
                        alt={job.company?.company_name || ''}
                        className="logo-filter"
                        width={78}
                        height={90}
                        layout="responsive"
                      />
                    </div>
                    <div className="col-sm-8 m-text-left">
                      <a target="_blank" href={'/job/' + job.job_slug}>
                        <p className="p-18">{job.job_title}</p>
                      </a>
                      <p className="p-16">{job.company?.company_name}</p>
                      {job.is_featured ? (
                        <button className="pro">
                          Featured
                          <img
                            src={process.env.NEXT_PUBLIC_BASE_URL + 'images/pro.png'}
                            alt=" pro"
                            className="w-25 "
                            width={25}
                            height={25}
                            // layout='responsive'
                          />
                        </button>
                      ) : (
                        ''
                      )}
                      <ul className="full-time">
                        <li>
                          {job.job_type ? (
                            <>
                              <i className="fa-solid fa-business-time"></i>{' '}
                              {job.job_type === 'parttime'
                                ? 'Part-Time'
                                : job.job_type === 'fulltime'
                                  ? 'Full-Time'
                                  : job.job_type === 'contract'
                                    ? 'Contract'
                                    : job.job_type === 'freelance'
                                      ? 'Freelance'
                                      : ' '}
                            </>
                          ) : (
                            ''
                          )}
                        </li>
                        <li>
                          <i className="fa-solid fa-location-dot"></i> {job.country?.country_name}
                        </li>
                      </ul>
                    </div>
                    <div className="col-sm-3 text-right  m-text-left">
                      <Dropdown overlay={menu(job)} trigger={['click']}>
                        <Button type="primary" icon={<DownOutlined />}>
                          Actions
                        </Button>
                      </Dropdown>
                    </div>
                  </div>
                  <p className="posted w-100 m-text-left">{formatDate(job.created_at)}</p>
                </div>
              ))}
              {paginationMeta && (
                <Pagination
                  onChange={onShowSizeChange}
                  total={paginationMeta.total}
                  onShowSizeChange={onShowSizeChange}
                  current={paginationMeta.current_page}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
