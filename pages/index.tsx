import React from 'react';
import Head from 'next/head';
import {getAllNewAdminSettings} from '@/lib/adminapi';
import {Settings} from '@/lib/types';
import FrontendHome from '../components/Frontend/Home/Home';
import {getAllCountries} from '@/lib/frontendapi';
import {getUserTestimonials} from '@/lib/getTestimonials';
import {getAllJobsWithId} from '@/lib/employeeapi';
import {getSectorsList} from '@/lib/ApiAdapter';

interface HomeProps {
  settings: Settings;
  countries: any;
  testimonials: any;
  latestJob: any;
  sectors: any;
}

function Home({settings, countries, testimonials, latestJob, sectors}: HomeProps) {
  return (
    <>
      <Head>
        <title>{settings?.homepage_meta_title ? settings?.homepage_meta_title : 'Talent Point'}</title>
        <meta
          name="description"
          content={settings?.homepage_meta_description ? settings?.homepage_meta_description : ''}
        />
        {/* Preload critical resources for mobile performance */}
        <link rel="preload" href="/images/home/<USER>" as="image" media="(max-width: 768px)" />
        <link rel="preload" href="/images/home/<USER>" as="image" media="(min-width: 769px)" />
        <link rel="preload" href="/icons/search.svg" as="image" />
        <link rel="preload" href="/icons/location.svg" as="image" />
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        {/* Critical CSS for above-the-fold content */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            .home-hero-section{background:#000e1f;border-radius:0 0 80px 80px}
            .home-hero-bg{position:relative;overflow:hidden}
            .home-hero-container{padding:169px 72px 220px 72px;display:flex;flex-direction:column;gap:32px}
            .hero-content{display:flex;flex-direction:column;gap:8px;text-align:center}
            .hero-content h4{color:#fff;text-align:center;font-size:46px;font-weight:700;line-height:120%;margin:0}
            .hero-content span{color:#0070f5}
            .hero-content p{color:#f9f9f9;text-align:center;font-size:26px;font-weight:500;line-height:120%;margin:0}
            @media screen and (max-width:768px){
              .home-hero-container{padding:40px 12px 216px 12px;gap:40px}
              .hero-content h4{font-size:32px;line-height:110%}
              .hero-content p{font-size:20px}
            }
          `,
          }}
        />
      </Head>
      <FrontendHome
        settings={settings}
        countries={countries}
        testimonials={testimonials}
        latestJob={latestJob}
        sectors={sectors}
      />
    </>
  );
}
export default React.memo(Home);

// export async function getServerSideProps() {
//   let settings = [];
//   let countries = [];

//   try {
//     const response = await getAllNewAdminSettings();
//     settings = response || [];
//   } catch (error) {
//     console.error(error);
//   }

//   try {
//     countries = await getAllCountries();
//   } catch (error) {
//     console.error(error);
//   }

//   return {
//     props: {
//       settings,
//       countries,
//     },
//   };
// }
export async function getStaticProps() {
  try {
    // Parallelize API calls
    const [settingsResponse, countriesResponse, userTestimonitals, latestJobResponse, sectorResponse] =
      await Promise.all([
        getAllNewAdminSettings(),
        getAllCountries(),
        getUserTestimonials(),
        getAllJobsWithId(),
        getSectorsList(),
      ]);

    const settings = settingsResponse || [];
    const countries = countriesResponse || [];
    const testimonials = userTestimonitals || [];
    const latestJob = latestJobResponse?.data || [];
    const sectors = sectorResponse || [];

    return {
      props: {
        settings,
        countries,
        testimonials,
        latestJob,
        sectors,
      },
      // Revalidate every 5 minutes (300 seconds)
      revalidate: 300,
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      props: {
        settings: [],
        countries: [],
        testimonials: [],
        latestJob: [],
        sectors: [],
      },
      // Revalidate every 5 minutes even on error
      revalidate: 300,
    };
  }
}
