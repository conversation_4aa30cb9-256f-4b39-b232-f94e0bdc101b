import React from 'react';
import Head from 'next/head';
import {getAllNewAdminSettings} from '@/lib/adminapi';
import {Settings} from '@/lib/types';
import FrontendHome from '../components/Frontend/Home/Home';
import {getAllCountries} from '@/lib/frontendapi';
import {getUserTestimonials} from '@/lib/getTestimonials';
import {getAllJobsWithId} from '@/lib/employeeapi';
import {getSectorsList} from '@/lib/ApiAdapter';

interface HomeProps {
  settings: Settings;
  countries: any;
  testimonials: any;
  latestJob: any;
  sectors: any;
}

function Home({settings, countries, testimonials, latestJob, sectors}: HomeProps) {
  return (
    <>
      <Head>
        <title>{settings?.homepage_meta_title ? settings?.homepage_meta_title : 'Talent Point'}</title>
        <meta
          name="description"
          content={settings?.homepage_meta_description ? settings?.homepage_meta_description : ''}
        />

        {/* WebP detection and critical resource preloading */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                var webP = new Image();
                webP.onload = webP.onerror = function () {
                  if (webP.height == 2) {
                    document.documentElement.classList.add('webp');
                  }
                };
                webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
              })();
            `,
          }}
        />

        {/* Preload critical WebP images with fallbacks */}
        <link
          rel="preload"
          href="/images/home/<USER>"
          as="image"
          type="image/webp"
          media="(min-width: 769px)"
        />
        <link rel="preload" href="/images/home/<USER>" as="image" media="(min-width: 769px)" />
        <link
          rel="preload"
          href="/images/home/<USER>"
          as="image"
          type="image/webp"
          media="(max-width: 768px)"
        />
        <link rel="preload" href="/images/home/<USER>" as="image" media="(max-width: 768px)" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />

        {/* Critical CSS for above-the-fold content */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
              .home-hero-section{background:#000e1f;border-radius:0 0 80px 80px}
              .home-hero-bg{background:url('/images/home/<USER>');background-size:cover;background-repeat:no-repeat;position:relative}
              .webp .home-hero-bg{background:url('/images/home/<USER>')}
              @media (max-width:768px){
                .home-hero-bg{background:url('/images/home/<USER>');background-repeat:no-repeat;background-position:center}
                .webp .home-hero-bg{background:url('/images/home/<USER>')}
              }
            `,
          }}
        />
      </Head>
      <FrontendHome
        settings={settings}
        countries={countries}
        testimonials={testimonials}
        latestJob={latestJob}
        sectors={sectors}
      />
    </>
  );
}
export default React.memo(Home);

// export async function getServerSideProps() {
//   let settings = [];
//   let countries = [];

//   try {
//     const response = await getAllNewAdminSettings();
//     settings = response || [];
//   } catch (error) {
//     console.error(error);
//   }

//   try {
//     countries = await getAllCountries();
//   } catch (error) {
//     console.error(error);
//   }

//   return {
//     props: {
//       settings,
//       countries,
//     },
//   };
// }
export async function getStaticProps() {
  try {
    // Parallelize API calls
    const [settingsResponse, countriesResponse, userTestimonitals, latestJobResponse, sectorResponse] =
      await Promise.all([
        getAllNewAdminSettings(),
        getAllCountries(),
        getUserTestimonials(),
        getAllJobsWithId(),
        getSectorsList(),
      ]);

    const settings = settingsResponse || [];
    const countries = countriesResponse || [];
    const testimonials = userTestimonitals || [];
    const latestJob = latestJobResponse?.data || [];
    const sectors = sectorResponse || [];

    return {
      props: {
        settings,
        countries,
        testimonials,
        latestJob,
        sectors,
      },
      // Revalidate every 5 minutes (300 seconds)
      revalidate: 300,
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      props: {
        settings: [],
        countries: [],
        testimonials: [],
        latestJob: [],
        sectors: [],
      },
      // Revalidate every 5 minutes even on error
      revalidate: 300,
    };
  }
}
