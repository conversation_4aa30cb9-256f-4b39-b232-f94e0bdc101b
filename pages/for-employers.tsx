import React from 'react';
import Head from 'next/head';

import {getAllNewAdminSettings} from '@/lib/adminapi';
import {Settings} from '@/lib/types';
import {useRouter} from 'next/router';
import FrontendHomeEmployer from '../components/Frontend/HomeEmployer';

interface HomeEmployerProps {
  settings: Settings;
}

export default function HomeEmployer({settings}: HomeEmployerProps) {
  const router = useRouter();

  return (
    <>
      <Head>
        <title>{settings.carrer_meta_title ? settings.carrer_meta_title : 'Talent Point'}</title>
        <meta name="description" content={settings.carrer_meta_description ? settings.carrer_meta_description : ''} />
        {/* Open Graph tags */}
        <meta
          property="og:title"
          content={
            settings.carrer_meta_title
              ? settings.carrer_meta_title
              : `Expert Career Tips, Guides, and Advice | Your Ultimate Job Portal Resource`
          }
        />
        <meta
          property="og:description"
          content={
            settings.blog_listing_meta_description
              ? settings.blog_listing_meta_description
              : `Discover the freshest job openings, expert CV and resume advice, and stay updated on HR hiring trends. TheTalentpoint is your go-to destination for career success. Explore now.`
          }
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@theTalentPoint" />
        <meta
          name="twitter:title"
          content={
            settings.carrer_meta_title
              ? settings.carrer_meta_title
              : `Expert Career Tips, Guides, and Advice | Your Ultimate Job Portal Resource`
          }
        />
        <meta
          name="twitter:description"
          content={
            settings.blog_listing_meta_description
              ? settings.blog_listing_meta_description
              : `Discover the freshest job openings, expert CV and resume advice, and stay updated on HR hiring trends. TheTalentpoint is your go-to destination for career success. Explore now.`
          }
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head>
      <FrontendHomeEmployer />
    </>
  );
}

export async function getStaticProps() {
  try {
    const response = await getAllNewAdminSettings();
    return {
      props: {settings: response || {}},
      // Revalidate every hour for settings
      revalidate: 3600,
    };
  } catch (error) {
    console.error('Error fetching settings:', error);
    return {
      props: {settings: {}},
      revalidate: 3600,
    };
  }
}
