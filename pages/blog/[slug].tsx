import React from 'react';
import Head from 'next/head';
import {getSingleBlogBySlug} from '@/lib/frontendapi';
import {getAllBlogs} from '@/lib/adminapi';
import SingleBlog from '@/components/Frontend/SingleBlog';

import {Blogs} from '@/lib/types';

interface SingleCountryPageProps {
  blogPost: Blogs;
  blog: Blogs[];
}

export default function SingleCountryPage({blogPost, blog}: SingleCountryPageProps) {
  return (
    <>
      <Head>
        <title>{blogPost?.meta_tag ? blogPost?.meta_tag : 'Talent Point'}</title>
        <meta name="description" content={blogPost?.meta_desc ? blogPost?.meta_desc : ''} />
      </Head>
      <SingleBlog blogPost={blogPost} blog={blog} />
    </>
  );
}

export async function getStaticPaths() {
  try {
    // Fetch all blogs to get their slugs
    const blogsResponse = await getAllBlogs({pageSize: 1000, page: 1});
    const blogs = blogsResponse?.data?.data || [];

    // Generate paths for all blog slugs
    const paths = blogs
      .filter((blog: Blogs) => blog.slug)
      .map((blog: Blogs) => ({
        params: {slug: blog.slug},
      }));

    return {
      paths,
      // Enable ISR for new blog posts
      fallback: 'blocking',
    };
  } catch (error) {
    console.error('Error fetching blog paths:', error);
    return {
      paths: [],
      fallback: 'blocking',
    };
  }
}

export async function getStaticProps({params}: {params: {slug: string}}) {
  const {slug} = params;

  try {
    const postResponse = await getSingleBlogBySlug(slug);

    if (!postResponse || postResponse.status === false || !postResponse.entry) {
      return {
        notFound: true,
      };
    }

    return {
      props: {
        blogPost: postResponse.entry,
        blog: postResponse.last_entries || [],
      },
      // Revalidate every 30 minutes for blog posts
      revalidate: 1800,
    };
  } catch (error) {
    console.error('Error fetching blog data:', error);
    return {
      notFound: true,
    };
  }
}
