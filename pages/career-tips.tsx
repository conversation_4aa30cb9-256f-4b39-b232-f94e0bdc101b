import React from 'react';

import {getAllNewAdminSettings} from '@/lib/adminapi';
import {Settings} from '@/lib/types';
import Head from 'next/head';

import FrontendCareerTips from '../components/Frontend/CareerTips';

interface CareerTipProps {
  settings: Settings;
}
import {useRouter} from 'next/router';

export default function CareerTip({settings}: CareerTipProps) {
  const router = useRouter();

  return (
    <>
      <Head>
        <title>{settings.carrer_meta_title ? settings.carrer_meta_title : 'Talent Point'}</title>
        <meta name="description" content={settings.carrer_meta_description ? settings.carrer_meta_description : ''} />
        {/* Open Graph tags */}
        <meta property="og:title" content={settings.carrer_meta_title ? settings.carrer_meta_title : `Talent Point`} />
        <meta
          property="og:description"
          content={
            settings.carrer_meta_description
              ? settings.carrer_meta_description
              : `Get Expert Career  tips, guides, and to clarify your doubts, map out your future aspirations, and uncover exciting career opportunities.`
          }
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@theTalentPoint" />
        <meta name="twitter:title" content={settings.carrer_meta_title ? settings.carrer_meta_title : `Talent Point`} />
        <meta
          name="twitter:description"
          content={
            settings.carrer_meta_description
              ? settings.carrer_meta_description
              : `Get Expert Career  tips, guides, and to clarify your doubts, map out your future aspirations, and uncover exciting career opportunities.`
          }
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head>
      <FrontendCareerTips />
    </>
  );
}

export async function getStaticProps() {
  try {
    const response = await getAllNewAdminSettings();
    return {
      props: {settings: response || {}},
      // Revalidate every hour for settings
      revalidate: 3600,
    };
  } catch (error) {
    console.error('Error fetching settings:', error);
    return {
      props: {settings: {}},
      revalidate: 3600,
    };
  }
}
