const fs = require('fs');
const path = require('path');
const { getAllBlogs } = require('../lib/adminapi');

const SITE_DATA_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.thetalentpoint.com/';

function generateSiteMap(blogs, authors) {
  const lastModifiedAt = new Date().toISOString();

  return `<?xml version="1.0" encoding="UTF-8"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
     <!--Static pages-->
     <url>
       <loc>${SITE_DATA_URL}</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>1.00</priority>
     </url>
     <url>
       <loc>${SITE_DATA_URL}jobs-in-gulf</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>0.80</priority>
     </url>
     <url>
       <loc>${SITE_DATA_URL}career-tips</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>0.80</priority>
     </url>
     <url>
       <loc>${SITE_DATA_URL}about-us</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>0.80</priority>
     </url>
     <url>
       <loc>${SITE_DATA_URL}blog</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>0.80</priority>
     </url>
     <url>
       <loc>${SITE_DATA_URL}for-employers</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>0.80</priority>
     </url>
     <url>
       <loc>${SITE_DATA_URL}jobs-by-location</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>0.80</priority>
     </url>
     <url>
       <loc>${SITE_DATA_URL}author</loc>
       <lastmod>${lastModifiedAt}</lastmod>
       <priority>0.80</priority>
     </url>
     ${blogs?.data?.data?.map(({slug, created_at, updated_at}) => {
       return `
       <url>
         <loc>${`${SITE_DATA_URL}blog/${slug}`}</loc>
         <lastmod>${created_at != null ? created_at : updated_at}</lastmod>
         <priority>0.64</priority>
       </url>`;
     }).join('') || ''}
     ${authors?.data?.map(({slug, created_at, updated_at}) => {
       return `
       <url>
         <loc>${`${SITE_DATA_URL}author/${slug}`}</loc>
         <lastmod>${created_at != null ? created_at : updated_at}</lastmod>
         <priority>0.64</priority>
       </url>`;
     }).join('') || ''}
   </urlset>`;
}

async function generateSitemap() {
  try {
    console.log('Generating sitemap...');
    
    // Fetch blogs and authors data
    const [blogsResponse, authorsResponse] = await Promise.all([
      getAllBlogs({ pageSize: 1000, page: 1 }),
      fetch(process.env.NEXT_PUBLIC_API_URL + '/author').then(res => res.json()).catch(() => ({ data: [] }))
    ]);

    // Generate sitemap
    const sitemap = generateSiteMap(blogsResponse, authorsResponse);
    
    // Write sitemap to public directory
    const publicDir = path.join(process.cwd(), 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }
    
    fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), sitemap);
    console.log('Sitemap generated successfully at public/sitemap.xml');
    
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Generate a basic sitemap with static pages only
    const basicSitemap = generateSiteMap({ data: { data: [] } }, { data: [] });
    fs.writeFileSync(path.join(process.cwd(), 'public', 'sitemap.xml'), basicSitemap);
    console.log('Basic sitemap generated due to error');
  }
}

// Run if called directly
if (require.main === module) {
  generateSitemap();
}

module.exports = { generateSitemap };
