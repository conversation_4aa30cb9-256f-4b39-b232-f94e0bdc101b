// Global TypeScript declarations to extend HTML elements with Next.js Image props

declare namespace React {
  interface ImgHTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
    // Next.js Image component props that might be used on img tags
    priority?: boolean;
    layout?: 'fill' | 'fixed' | 'intrinsic' | 'responsive' | 'raw' | string;
    fill?: boolean;
    unoptimized?: boolean;
    objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down' | string;
    objectPosition?: string;
    placeholder?: 'blur' | 'empty';
    blurDataURL?: string;
    quality?: number;
    sizes?: string;
    loader?: (resolverProps: {
      src: string;
      width: number;
      quality?: number;
    }) => string;
    onLoadingComplete?: (result: {
      naturalWidth: number;
      naturalHeight: number;
    }) => void;
    onLoad?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
    onError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  }
}

export {};
