# Next.js Image Component Usage Guide

## Problem

We encountered TypeScript build errors when HTML `<img>` tags were used with Next.js Image component props like `priority`, `layout`, `fill`, `unoptimized`, `objectFit`, and `objectPosition`.

## Error Example

```typescript
// ❌ WRONG - This will cause TypeScript errors
<img
  src="/image.jpg"
  alt="Description"
  width={100}
  height={100}
  priority  // ← This prop doesn't exist on HTML img elements
  layout="responsive"  // ← This prop doesn't exist on HTML img elements
/>
```

## Solution

Use the Next.js `Image` component instead:

```typescript
// ✅ CORRECT
import Image from 'next/image';

<Image
  src="/image.jpg"
  alt="Description"
  width={100}
  height={100}
  priority
  layout="responsive"
/>
```

## Next.js Image Props vs HTML img Props

### Next.js Image Component Props:
- `priority` - Load image with high priority
- `layout` - How the image should resize ("responsive", "fill", etc.)
- `fill` - Fill the parent container
- `unoptimized` - Skip image optimization
- `objectFit` - How image should fit in container
- `objectPosition` - Position of image in container

### HTML img Element Props:
- `src`, `alt`, `width`, `height`, `className` - Standard HTML attributes
- Does NOT support Next.js specific props

## Prevention Measures

1. **ESLint Rule**: We've enabled `@next/next/no-img-element` to warn about img usage
2. **TypeScript Strict Mode**: Build will fail on TypeScript errors
3. **Pre-commit Hook**: Runs linting and type checking before commits
4. **VS Code Settings**: Real-time ESLint validation

## Quick Fix

When you see this error:
1. Import `Image` from `next/image` at the top of the file
2. Change `<img>` to `<Image>`
3. Keep all the props the same

## Best Practices

- Always use Next.js `Image` component for better performance
- Only use HTML `<img>` for external images or when optimization isn't needed
- Import `Image` component at the top of your files
- Use appropriate `alt` text for accessibility
