// Custom ESLint rule to catch img tags with Next.js Image props
module.exports = {
  rules: {
    'no-img-with-next-props': {
      meta: {
        type: 'error',
        docs: {
          description: 'Disallow img tags with Next.js Image component props',
          category: 'Best Practices',
        },
        fixable: 'code',
        schema: [],
      },
      create(context) {
        const nextImageProps = ['priority', 'layout', 'fill', 'unoptimized', 'objectFit', 'objectPosition'];
        
        return {
          JSXElement(node) {
            if (node.openingElement.name.name === 'img') {
              const hasNextProps = node.openingElement.attributes.some(attr => {
                return attr.type === 'JSXAttribute' && nextImageProps.includes(attr.name.name);
              });
              
              if (hasNextProps) {
                context.report({
                  node,
                  message: 'img tag should not use Next.js Image component props. Use <Image> from next/image instead.',
                  fix(fixer) {
                    return fixer.replaceText(node.openingElement.name, 'Image');
                  }
                });
              }
            }
          }
        };
      }
    }
  }
};
